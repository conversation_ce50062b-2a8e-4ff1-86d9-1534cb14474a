// Expose functions to global scope for testing
(globalThis as any).main = main;
(globalThis as any).doGet = doGet;
(globalThis as any).doPost = doPost;

// Configuration with validation
const CONFIG = {
  SPREADSHEET_ID:
    PropertiesService.getScriptProperties().getProperty("SPREADSHEET_ID") ??
    "122UokHhXHUrkwT13OSiR23g-6S3TkpdSzfS6JmO1EMg",
  FOLDER_ID:
    PropertiesService.getScriptProperties().getProperty("FOLDER_ID") ??
    "136jOm1HZeHWK_KvxTVuZ1oywo8b5Oz4q",
  MAX_DOCUMENT_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_NAME_LENGTH: 255,
  MAX_DESCRIPTION_LENGTH: 1000,
  VALID_CATEGORIES: ["PJ概要資料", "タスク"] as const,
  VALID_STATUSES: ["未着手", "進行中", "完了"] as const,
  REQUIRED_PROPERTIES: [
    "name",
    "category",
    "created_by",
    "updated_by",
    "description",
  ] as const,
};

// Enhanced error classes
class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = "ValidationError";
  }
}

class ConfigurationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "ConfigurationError";
  }
}

class GoogleServiceError extends Error {
  constructor(message: string, public service?: string) {
    super(message);
    this.name = "GoogleServiceError";
  }
}

// Type definitions remain the same
type CommonProperties = {
  name: string;
  url: string;
  category: "PJ概要資料" | "タスク";
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by: string;
  related_deps: string[];
  related_docs: string[];
  description: string;
};

type ProjectProperties = CommonProperties & {
  duration: string;
  status: "未着手" | "進行中" | "完了";
  assignee: string;
};

type TaskProperties = CommonProperties & {
  due_date: string;
  status: "未着手" | "進行中" | "完了";
  assignee: string;
};

type Properties = ProjectProperties | TaskProperties;

/**
 * The main function calls the testAPI function.
 */
function main(): void {
  testAPI();
}

/**
 * The function `testAPI` validates configuration and creates a test document with specified
 * properties.
 */
function testAPI(): void {
  try {
    // Validate configuration first
    validateConfiguration();

    const createDocResponse = createDocument({
      md: "# Test Document\n\nThis is a test document.",
      properties: {
        name: "Test Document",
        url: "https://docs.google.com/document/d/1234567890",
        category: "PJ概要資料",
        created_at: new Date(),
        updated_at: new Date(),
        created_by: "Taira Urakawa",
        updated_by: "Taira Urakawa",
        related_deps: ["全社", "AI"],
        related_docs: [],
        description: "This is a test document.",
        duration: "1 month",
        status: "未着手",
        assignee: "Taira Urakawa",
      },
    });
    Logger.log(JSON.parse(createDocResponse.getContent()));
  } catch (error) {
    Logger.log(`Test failed: ${error}`);
  }
}

/**
 * The function `validateConfiguration` checks if required configuration values are set and tests
 * access to a spreadsheet and folder based on those configurations.
 */
function validateConfiguration(): void {
  if (!CONFIG.SPREADSHEET_ID) {
    throw new ConfigurationError("SPREADSHEET_ID is not configured");
  }
  if (!CONFIG.FOLDER_ID) {
    throw new ConfigurationError("FOLDER_ID is not configured");
  }

  try {
    // Test spreadsheet access
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName("Master");
    if (!sheet) {
      throw new ConfigurationError("Master sheet not found in spreadsheet");
    }
  } catch (error) {
    throw new ConfigurationError(`Cannot access spreadsheet: ${error}`);
  }

  try {
    // Test folder access
    DriveApp.getFolderById(CONFIG.FOLDER_ID);
  } catch (error) {
    throw new ConfigurationError(`Cannot access folder: ${error}`);
  }
}

/**
 * The function `validateMarkdown` in TypeScript validates a given Markdown content to ensure it is not
 * empty, is of type string, and does not exceed a maximum document size specified in the `CONFIG`
 * object.
 * @param {string} md - The `validateMarkdown` function is used to validate a Markdown content string.
 * It checks if the provided Markdown content meets certain criteria before processing it further.
 */
function validateMarkdown(md: string): void {
  if (!md || typeof md !== "string") {
    throw new ValidationError(
      "Markdown content is required and must be a string",
      "md"
    );
  }
  if (md.trim().length === 0) {
    throw new ValidationError("Markdown content cannot be empty", "md");
  }
  if (new Blob([md]).size > CONFIG.MAX_DOCUMENT_SIZE) {
    throw new ValidationError(
      `Document size exceeds maximum of ${CONFIG.MAX_DOCUMENT_SIZE} bytes`,
      "md"
    );
  }
}

/**
 * The function `validateDocumentId` checks if a given document ID is a valid string containing only
 * alphanumeric characters, underscores, and hyphens.
 * @param {string} docId - The `validateDocumentId` function takes a `docId` parameter of type string.
 * It validates the `docId` to ensure it is not empty, is a string, and contains only alphanumeric
 * characters, underscores, and hyphens. If the `docId` does not meet these criteria
 */
function validateDocumentId(docId: string): void {
  if (!docId || typeof docId !== "string") {
    throw new ValidationError(
      "Document ID is required and must be a string",
      "docId"
    );
  }
  if (!/^[a-zA-Z0-9_-]+$/.test(docId)) {
    throw new ValidationError(
      "Document ID contains invalid characters",
      "docId"
    );
  }
}

/**
 * The function `validateProperties` in TypeScript validates various properties of an object based on
 * specific criteria and throws validation errors if any criteria are not met.
 * @param {Properties} properties - The `properties` parameter in the `validateProperties` function is
 * an object that contains various fields representing properties of a document. These properties
 * include `name`, `category`, `description`, `related_deps`, `related_docs`, `created_by`,
 * `updated_by`, and additional fields specific to different categories
 */
function validateProperties(properties: Properties): void {
  if (!properties || typeof properties !== "object") {
    throw new ValidationError("Properties object is required", "properties");
  }

  // Check required properties
  for (const field of CONFIG.REQUIRED_PROPERTIES) {
    if (!properties[field as keyof Properties]) {
      throw new ValidationError(`${field} is required`, field);
    }
  }

  // Validate name
  if (
    typeof properties.name !== "string" ||
    properties.name.trim().length === 0
  ) {
    throw new ValidationError("Name must be a non-empty string", "name");
  }
  if (properties.name.length > CONFIG.MAX_NAME_LENGTH) {
    throw new ValidationError(
      `Name exceeds maximum length of ${CONFIG.MAX_NAME_LENGTH} characters`,
      "name"
    );
  }

  // Validate category
  if (!CONFIG.VALID_CATEGORIES.includes(properties.category)) {
    throw new ValidationError(
      `Category must be one of: ${CONFIG.VALID_CATEGORIES.join(", ")}`,
      "category"
    );
  }

  // Validate description
  if (typeof properties.description !== "string") {
    throw new ValidationError("Description must be a string", "description");
  }
  if (properties.description.length > CONFIG.MAX_DESCRIPTION_LENGTH) {
    throw new ValidationError(
      `Description exceeds maximum length of ${CONFIG.MAX_DESCRIPTION_LENGTH} characters`,
      "description"
    );
  }

  // Validate arrays
  if (!Array.isArray(properties.related_deps)) {
    throw new ValidationError("related_deps must be an array", "related_deps");
  }
  if (!Array.isArray(properties.related_docs)) {
    throw new ValidationError("related_docs must be an array", "related_docs");
  }

  // Validate user fields
  if (
    typeof properties.created_by !== "string" ||
    properties.created_by.trim().length === 0
  ) {
    throw new ValidationError(
      "created_by must be a non-empty string",
      "created_by"
    );
  }
  if (
    typeof properties.updated_by !== "string" ||
    properties.updated_by.trim().length === 0
  ) {
    throw new ValidationError(
      "updated_by must be a non-empty string",
      "updated_by"
    );
  }

  // Category-specific validation
  if (properties.category === "PJ概要資料") {
    const projectProps = properties as ProjectProperties;
    if (!projectProps.duration || typeof projectProps.duration !== "string") {
      throw new ValidationError(
        "duration is required for project documents",
        "duration"
      );
    }
    if (!CONFIG.VALID_STATUSES.includes(projectProps.status)) {
      throw new ValidationError(
        `status must be one of: ${CONFIG.VALID_STATUSES.join(", ")}`,
        "status"
      );
    }
    if (!projectProps.assignee || typeof projectProps.assignee !== "string") {
      throw new ValidationError(
        "assignee is required for project documents",
        "assignee"
      );
    }
  } else if (properties.category === "タスク") {
    const taskProps = properties as TaskProperties;
    if (!taskProps.due_date || typeof taskProps.due_date !== "string") {
      throw new ValidationError(
        "due_date is required for task documents",
        "due_date"
      );
    }
    // Validate due_date format (basic ISO date check)
    if (!/^\d{4}-\d{2}-\d{2}/.test(taskProps.due_date)) {
      throw new ValidationError(
        "due_date must be in YYYY-MM-DD format",
        "due_date"
      );
    }
    if (!CONFIG.VALID_STATUSES.includes(taskProps.status)) {
      throw new ValidationError(
        `status must be one of: ${CONFIG.VALID_STATUSES.join(", ")}`,
        "status"
      );
    }
    if (!taskProps.assignee || typeof taskProps.assignee !== "string") {
      throw new ValidationError(
        "assignee is required for task documents",
        "assignee"
      );
    }
  }
}

/**
 * The function `doGet` processes incoming HTTP requests based on specified actions, handling errors
 * and returning appropriate responses.
 * @param e - The `e` parameter in the `doGet` function is of type `GoogleAppsScript.Events.DoGet`,
 * which represents the event object containing information about the HTTP GET request made to the web
 * app. This object includes parameters, context, and other details related to the request.
 * @returns The `doGet` function returns a `GoogleAppsScript.Content.TextOutput` object. The specific
 * content of the returned object depends on the action parameter provided in the request. If the
 * action is "database", the function will return the result of the `retrieveMasterDB` function. If the
 * action is "document", the function will return the result of the `getDocumentAsMarkdown` function
 * with
 */
function doGet(
  e: GoogleAppsScript.Events.DoGet
): GoogleAppsScript.Content.TextOutput {
  try {
    validateConfiguration();

    const action: string | undefined = e.parameter.action;
    if (!action) {
      throw new ValidationError("action parameter is required", "action");
    }

    switch (action) {
      case "database":
        return retrieveMasterDB();
      case "document":
        const docId: string | undefined = e.parameter.docId;
        if (!docId) {
          throw new ValidationError(
            "docId parameter is required for document action",
            "docId"
          );
        }
        validateDocumentId(docId);
        return getDocumentAsMarkdown({ docId: docId });
      default:
        throw new ValidationError(
          `Invalid action: ${action}. Valid actions: database, document`,
          "action"
        );
    }
  } catch (error) {
    return handleError(error);
  }
}

/**
 * The function `doPost` processes incoming HTTP POST requests by validating the request body and
 * performing different actions based on the specified action.
 * @param e - The `e` parameter in the `doPost` function represents the event object that contains
 * information about the HTTP POST request made to the web app. It is of type
 * `GoogleAppsScript.Events.DoPost`. This object includes data such as the request headers, parameters,
 * and body content of the POST
 * @returns The `doPost` function returns a `GoogleAppsScript.Content.TextOutput` object. The specific
 * content of this object will depend on the outcome of the `switch` statement inside the function. The
 * function will return the result of the corresponding action function (e.g., `createDocument`,
 * `overwriteWithMarkdown`, `updateProperties`) if the action is valid and all validations pass. If
 * there is
 */
function doPost(
  e: GoogleAppsScript.Events.DoPost
): GoogleAppsScript.Content.TextOutput {
  try {
    validateConfiguration();

    if (!e.postData || !e.postData.contents) {
      throw new ValidationError("Request body is required");
    }

    let requestBody: any;
    try {
      requestBody = JSON.parse(e.postData.contents);
    } catch (parseError) {
      throw new ValidationError("Invalid JSON in request body");
    }

    const action: string = requestBody.action;
    if (!action) {
      throw new ValidationError("action is required in request body", "action");
    }

    switch (action) {
      case "create_document":
        validateMarkdown(requestBody.md);
        validateProperties(requestBody.properties);
        return createDocument(requestBody);
      case "update_document":
        validateDocumentId(requestBody.docId);
        validateMarkdown(requestBody.md);
        return overwriteWithMarkdown(requestBody);
      case "update_properties":
        validateDocumentId(requestBody.docId);
        validateProperties(requestBody.properties);
        return updateProperties(requestBody);
      default:
        throw new ValidationError(
          `Invalid action: ${action}. Valid actions: create_document, update_document, update_properties`,
          "action"
        );
    }
  } catch (error) {
    return handleError(error);
  }
}

/* The above code is a TypeScript function that is supposed to retrieve the master database in a Google
Apps Script project. The function is named `retrieveMasterDB` and it is expected to return a
`GoogleAppsScript.Content.TextOutput` object. However, the function body is represented by
placeholder characters (` */
function retrieveMasterDB(): GoogleAppsScript.Content.TextOutput {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName("Master");

    if (!sheet) {
      throw new GoogleServiceError("Master sheet not found", "Sheets");
    }

    const data: any[][] | undefined = sheet.getDataRange().getValues();

    if (!data || data.length < 1) {
      return createResponse(true, "Table data retrieved successfully", {
        data: [],
      });
    }

    if (data.length < 2) {
      return createResponse(true, "Table data retrieved successfully", {
        data: [],
      });
    }

    const headers: any[] | undefined = data.shift();
    if (!headers || headers.length === 0) {
      throw new GoogleServiceError(
        "No headers found in Master sheet",
        "Sheets"
      );
    }

    const jsonArray = data
      .map((row, index) => {
        const obj: { [key: string]: any } = {};
        headers.forEach((header, headerIndex) => {
          if (header && header.toString().trim()) {
            obj[header] = row[headerIndex];
          }
        });
        return obj;
      })
      .filter((obj) => Object.keys(obj).length > 0);

    return createResponse(true, "Table data retrieved successfully", {
      data: jsonArray,
      count: jsonArray.length,
    });
  } catch (error) {
    if (error instanceof GoogleServiceError) {
      throw error;
    }
    throw new GoogleServiceError(
      `Failed to retrieve database: ${error}`,
      "Sheets"
    );
  }
}

/**
 * The function `getDocumentAsMarkdown` retrieves a Google Docs document as Markdown format using its
 * ID.
 * @param params - The `getDocumentAsMarkdown` function takes a single parameter `params` which is an
 * object with a property `docId` of type string. This function is responsible for retrieving a Google
 * Document as Markdown format using the provided `docId`.
 * @returns The function `getDocumentAsMarkdown` returns a `GoogleAppsScript.Content.TextOutput`
 * object. This object contains the content of the document in Markdown format along with the document
 * ID.
 */
function getDocumentAsMarkdown(params: {
  docId: string;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const docId: string = params.docId;

    // Check if document exists and is accessible
    try {
      DocumentApp.openById(docId);
    } catch (docError) {
      throw new GoogleServiceError(
        `Document not found or not accessible: ${docId}`,
        "Docs"
      );
    }

    const response = UrlFetchApp.fetch(
      `https://docs.google.com/feeds/download/documents/export/Export?id=${docId}&exportFormat=md`,
      {
        method: "get",
        headers: {
          Authorization: "Bearer " + ScriptApp.getOAuthToken(),
        },
      }
    );

    if (response.getResponseCode() !== 200) {
      throw new GoogleServiceError(
        `Failed to export document. HTTP ${response.getResponseCode()}: ${response.getContentText()}`,
        "Drive"
      );
    }

    const md: string = response.getContentText();

    if (!md) {
      throw new GoogleServiceError(
        "Document export returned empty content",
        "Drive"
      );
    }

    return createResponse(true, "Document retrieved successfully", {
      content: md,
      docId: docId,
    });
  } catch (error) {
    if (error instanceof GoogleServiceError) {
      throw error;
    }
    throw new GoogleServiceError(`Failed to get document: ${error}`, "Docs");
  }
}

/**
 * The function `createDocument` creates a Google Docs document from Markdown content, adds it to a
 * spreadsheet, and returns information about the created document.
 * @param params - The `createDocument` function takes in two parameters:
 * @returns The function `createDocument` is returning a `GoogleAppsScript.Content.TextOutput` object.
 */
function createDocument(params: {
  md: string;
  properties: Properties;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const md: string = params.md;
    const properties: Properties = params.properties;

    // Create the document
    let docId: string;
    try {
      const file = Drive.Files.create(
        {
          name: properties.name,
          parents: [CONFIG.FOLDER_ID],
          mimeType: MimeType.GOOGLE_DOCS,
        },
        Utilities.newBlob(md, "text/markdown")
      );

      if (!file.id) {
        throw new GoogleServiceError(
          "Failed to create document - no ID returned",
          "Drive"
        );
      }
      docId = file.id;
    } catch (driveError) {
      throw new GoogleServiceError(
        `Failed to create document in Drive: ${driveError}`,
        "Drive"
      );
    }

    let doc: GoogleAppsScript.Document.Document;
    try {
      doc = DocumentApp.openById(docId);
    } catch (docError) {
      // Clean up the created file if we can't open it
      try {
        DriveApp.getFileById(docId).setTrashed(true);
      } catch (cleanupError) {
        Logger.log(`Failed to cleanup document ${docId}: ${cleanupError}`);
      }
      throw new GoogleServiceError(
        `Failed to open created document: ${docError}`,
        "Docs"
      );
    }

    // Add to spreadsheet
    try {
      const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
      const sheet = ss.getSheetByName("Master");

      if (!sheet) {
        throw new GoogleServiceError("Master sheet not found", "Sheets");
      }

      const rowData = [
        properties.name,
        doc.getUrl(),
        properties.category,
        new Date(),
        properties.created_by,
        new Date(),
        properties.updated_by,
        properties.related_deps.join(","),
        properties.related_docs.join(","),
        properties.description,
        properties.category === "PJ概要資料"
          ? (properties as ProjectProperties).duration
          : "",
        properties.category === "PJ概要資料"
          ? (properties as ProjectProperties).status
          : "",
        properties.category === "PJ概要資料"
          ? (properties as ProjectProperties).assignee
          : "",
        properties.category === "タスク"
          ? (properties as TaskProperties).due_date
          : "",
        properties.category === "タスク"
          ? (properties as TaskProperties).status
          : "",
        properties.category === "タスク"
          ? (properties as TaskProperties).assignee
          : "",
      ];

      sheet.appendRow(rowData);
    } catch (sheetError) {
      // Clean up the created document if spreadsheet update fails
      try {
        DriveApp.getFileById(docId).setTrashed(true);
      } catch (cleanupError) {
        Logger.log(`Failed to cleanup document ${docId}: ${cleanupError}`);
      }
      throw new GoogleServiceError(
        `Failed to update Master sheet: ${sheetError}`,
        "Sheets"
      );
    }

    return createResponse(true, "Document created successfully", {
      docId: docId,
      title: doc.getName(),
      url: doc.getUrl(),
    });
  } catch (error) {
    if (
      error instanceof GoogleServiceError ||
      error instanceof ValidationError
    ) {
      throw error;
    }
    throw new GoogleServiceError(
      `Failed to create document: ${error}`,
      "Unknown"
    );
  }
}

/**
 * The function `overwriteWithMarkdown` updates a Google Docs document with content from a Markdown
 * string.
 * @param params - The `overwriteWithMarkdown` function takes in two parameters:
 * @returns A GoogleAppsScript.Content.TextOutput object is being returned from the function
 * `overwriteWithMarkdown`.
 */
function overwriteWithMarkdown(params: {
  docId: string;
  md: string;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const docId: string = params.docId;
    const md: string = params.md;

    // Verify document exists
    let destDoc: GoogleAppsScript.Document.Document;
    try {
      destDoc = DocumentApp.openById(docId);
    } catch (docError) {
      throw new GoogleServiceError(
        `Document not found or not accessible: ${docId}`,
        "Docs"
      );
    }

    // Create temporary document
    let tmpId: string;
    try {
      const tmpFile = Drive.Files.create(
        {
          name: `tmp_${Date.now()}`,
          parents: [CONFIG.FOLDER_ID],
          mimeType: MimeType.GOOGLE_DOCS,
        },
        Utilities.newBlob(md, "text/markdown")
      );

      if (!tmpFile.id) {
        throw new GoogleServiceError(
          "Failed to create temporary document",
          "Drive"
        );
      }
      tmpId = tmpFile.id;
    } catch (driveError) {
      throw new GoogleServiceError(
        `Failed to create temporary document: ${driveError}`,
        "Drive"
      );
    }

    try {
      // Clear destination document
      const destBody = destDoc.getBody();
      destBody.clear();

      // Copy content from temporary document
      const srcDoc = DocumentApp.openById(tmpId);
      const srcBody = srcDoc.getBody();
      let lastDestItem: GoogleAppsScript.Document.ListItem | null = null;

      for (let i = 0; i < srcBody.getNumChildren(); i++) {
        const el = srcBody.getChild(i);

        if (el.getType() === DocumentApp.ElementType.LIST_ITEM) {
          const srcLI = el.asListItem();
          const newLI = destBody.appendListItem(srcLI.getText());

          newLI.setGlyphType(srcLI.getGlyphType());
          newLI.setNestingLevel(srcLI.getNestingLevel());
          newLI.setAttributes(srcLI.getAttributes());

          lastDestItem = newLI;
          continue;
        }

        switch (el.getType()) {
          case DocumentApp.ElementType.TABLE:
            destBody.appendTable(el.copy() as GoogleAppsScript.Document.Table);
            break;
          default:
            destBody.appendParagraph(
              el.copy() as GoogleAppsScript.Document.Paragraph
            );
        }
        lastDestItem = null;
      }
    } catch (updateError) {
      throw new GoogleServiceError(
        `Failed to update document content: ${updateError}`,
        "Docs"
      );
    } finally {
      // Always clean up temporary file
      try {
        DriveApp.getFileById(tmpId).setTrashed(true);
      } catch (cleanupError) {
        Logger.log(
          `Failed to cleanup temporary document ${tmpId}: ${cleanupError}`
        );
      }
    }

    return createResponse(true, "Document updated successfully", {
      docId: docId,
      title: destDoc.getName(),
    });
  } catch (error) {
    if (
      error instanceof GoogleServiceError ||
      error instanceof ValidationError
    ) {
      throw error;
    }
    throw new GoogleServiceError(
      `Failed to update document: ${error}`,
      "Unknown"
    );
  }
}

/**
 * The function `updateProperties` updates the properties of a document in Google Sheets based on the
 * provided parameters.
 * @param params - The `updateProperties` function takes in two parameters:
 * @returns The `updateProperties` function returns a `GoogleAppsScript.Content.TextOutput` object.
 * This object contains information about the success or failure of updating the properties, along with
 * details such as the document ID and the updated name.
 */
function updateProperties(params: {
  docId: string;
  properties: Properties;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const docId: string = params.docId;
    const properties: Properties = params.properties;

    // Verify document exists
    try {
      DocumentApp.openById(docId);
    } catch (docError) {
      throw new GoogleServiceError(
        `Document not found or not accessible: ${docId}`,
        "Docs"
      );
    }

    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName("Master");

    if (!sheet) {
      throw new GoogleServiceError("Master sheet not found", "Sheets");
    }

    const dataRange = sheet.getDataRange();
    const rows = dataRange.getValues();

    if (rows.length < 2) {
      throw new GoogleServiceError("No data found in Master sheet", "Sheets");
    }

    // Find row by document ID in URL column
    const rowIndex = rows.findIndex(
      (row) => row[1] && row[1].toString().includes(docId)
    );

    if (rowIndex === -1) {
      throw new ValidationError(
        `Document not found in Master DB: ${docId}`,
        "docId"
      );
    }

    const oldRow = rows[rowIndex];
    if (!oldRow) {
      throw new GoogleServiceError(
        "Failed to retrieve existing row data",
        "Sheets"
      );
    }

    // Construct updated row
    const updatedRow = [
      properties.name,
      oldRow[1], // URL remains unchanged
      properties.category,
      oldRow[3], // created_at remains unchanged
      oldRow[4], // created_by remains unchanged
      new Date(), // updated_at
      properties.updated_by,
      properties.related_deps.join(","),
      properties.related_docs.join(","),
      properties.description,
      properties.category === "PJ概要資料"
        ? (properties as ProjectProperties).duration
        : oldRow[10],
      properties.category === "PJ概要資料"
        ? (properties as ProjectProperties).status
        : oldRow[11],
      properties.category === "PJ概要資料"
        ? (properties as ProjectProperties).assignee
        : oldRow[12],
      properties.category === "タスク"
        ? (properties as TaskProperties).due_date
        : oldRow[13],
      properties.category === "タスク"
        ? (properties as TaskProperties).status
        : oldRow[14],
      properties.category === "タスク"
        ? (properties as TaskProperties).assignee
        : oldRow[15],
    ];

    try {
      sheet
        .getRange(rowIndex + 1, 1, 1, updatedRow.length)
        .setValues([updatedRow]);
    } catch (sheetError) {
      throw new GoogleServiceError(
        `Failed to update spreadsheet: ${sheetError}`,
        "Sheets"
      );
    }

    // Update document name in Drive
    try {
      DriveApp.getFileById(docId).setName(properties.name);
    } catch (driveError) {
      Logger.log(
        `Warning: Failed to update document name in Drive: ${driveError}`
      );
      // Don't throw error here as the main update succeeded
    }

    return createResponse(true, "Properties updated successfully", {
      docId: docId,
      name: properties.name,
    });
  } catch (error) {
    if (
      error instanceof GoogleServiceError ||
      error instanceof ValidationError
    ) {
      throw error;
    }
    throw new GoogleServiceError(
      `Failed to update properties: ${error}`,
      "Unknown"
    );
  }
}

/**
 * The function `handleError` in TypeScript handles different types of errors and logs them for
 * debugging purposes before returning a response.
 * @param {any} error - The `handleError` function is designed to handle different types of errors and
 * return a formatted response based on the type of error encountered. The function first determines
 * the type of error and extracts relevant information from the error object. It then logs the error
 * details for debugging purposes and returns a response object with the
 * @returns The `handleError` function is returning a Google Apps Script `TextOutput` object. The
 * `createResponse` function is being called with parameters `false`, `message`, and an object
 * containing `errorType` and `details`.
 */
function handleError(error: any): GoogleAppsScript.Content.TextOutput {
  let errorType = "UnknownError";
  let message = "An unknown error occurred";
  let details: any = null;

  if (error instanceof ValidationError) {
    errorType = "ValidationError";
    message = error.message;
    details = { field: error.field };
  } else if (error instanceof ConfigurationError) {
    errorType = "ConfigurationError";
    message = error.message;
  } else if (error instanceof GoogleServiceError) {
    errorType = "GoogleServiceError";
    message = error.message;
    details = { service: error.service };
  } else if (error instanceof Error) {
    message = error.message;
  } else {
    message = String(error);
  }

  // Log error for debugging
  Logger.log(`${errorType}: ${message}`);
  if (details) {
    Logger.log(`Details: ${JSON.stringify(details)}`);
  }

  return createResponse(false, message, {
    errorType: errorType,
    details: details,
  });
}

/**
 * The function `createResponse` generates a JSON response with success status, message, data,
 * timestamp, and version.
 * @param {boolean} success - The `success` parameter is a boolean value indicating whether the
 * operation was successful (`true`) or not (`false`).
 * @param {string} message - The `message` parameter in the `createResponse` function is a string that
 * represents a message or information that you want to include in the response. It can be used to
 * provide details about the outcome of an operation, any errors encountered, or any other relevant
 * information that you want to communicate back to
 * @param {any} data - The `data` parameter in the `createResponse` function is used to pass any
 * additional information or payload that you want to include in the response. This can be any type of
 * data, such as an object, array, string, number, etc. The `data` parameter allows you to customize
 * @returns The `createResponse` function returns a Google Apps Script `TextOutput` object with a JSON
 * response containing the properties `success`, `message`, `data`, `timestamp`, and `version`.
 */
function createResponse(
  success: boolean,
  message: string,
  data: any
): GoogleAppsScript.Content.TextOutput {
  const response = {
    success: success,
    message: message,
    data: data,
    timestamp: new Date().toISOString(),
    version: "1.1.0",
  };

  return ContentService.createTextOutput(
    JSON.stringify(response, null, 2)
  ).setMimeType(ContentService.MimeType.JSON);
}
